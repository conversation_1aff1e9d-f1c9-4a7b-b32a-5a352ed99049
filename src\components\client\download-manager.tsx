'use client'

import React from 'react'
import { useTranslations } from 'next-intl'
import DownloadCard from './download/DownloadCard'
import { useDownloader } from '../../hooks/download/useDownloader'

/**
 * 下载管理器组件
 * 集成 DownloadCard 组件到当前项目中
 */
export default function DownloadManager() {
  const t = useTranslations('downloader')
  
  // 使用下载器 Hook
  const {
    isLoading,
    error,
    downloadData,
    downloadProgress,
    startDownloadWithData,
    saveToLocal,
    savedFiles,
    updateDownloadDataFilename,
    stopLiveRecording
  } = useDownloader()

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <div className="w-full max-w-4xl mt-8">
        <div className="flex flex-col justify-center items-center p-6 gap-4 relative w-full h-80 bg-white shadow-lg rounded-lg">
          <div className="flex flex-col items-center gap-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <div className="text-gray-600 text-lg">
              {t('loading')}
            </div>
          </div>
        </div>
      </div>
    )
  }

  // 如果有错误，显示错误状态
  if (error) {
    return (
      <div className="w-full max-w-4xl mt-8">
        <div className="flex flex-col justify-center items-center p-6 gap-4 relative w-full h-80 bg-white shadow-lg rounded-lg border-red-200">
          <div className="flex flex-col items-center gap-4">
            <div className="text-red-500 text-lg">
              {t('error')}
            </div>
            <div className="text-gray-500 text-sm text-center max-w-md">
              {error}
            </div>
          </div>
        </div>
      </div>
    )
  }

  // 如果有下载数据，显示下载卡片
  if (downloadData) {
    return (
      <div className="w-full max-w-4xl mt-8">
        <DownloadCard
          downloadData={downloadData}
          downloadProgress={downloadProgress}
          startDownloadWithData={startDownloadWithData}
          saveToLocal={saveToLocal}
          savedFiles={savedFiles}
          updateDownloadDataFilename={updateDownloadDataFilename}
          stopLiveRecording={stopLiveRecording}
        />
      </div>
    )
  }

  // 默认占位符状态
  return (
    <div className="w-full max-w-4xl mt-8">
      <div className="flex flex-col justify-center items-center p-6 gap-4 relative w-full h-80 bg-white shadow-lg rounded-lg">
        <div className="flex flex-col items-center gap-4">
          <div className="text-gray-400 text-lg">
            {t('placeholder')}
          </div>
          <div className="text-gray-500 text-sm text-center max-w-md">
            {t('placeholderDesc')}
          </div>
        </div>
      </div>
    </div>
  )
}