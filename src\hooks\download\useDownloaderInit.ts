import { useEffect, useRef, useCallback } from 'react';
import type { DownloadItem, AppState } from '../../types/download';
import {
  getRequestIdFromUrl,
  generatePageTaskId,
  requestDownloadData,
  createMessageListener
} from '../../lib/download/messageUtils';

interface UseDownloaderInitProps {
  state: AppState;
  setPageTaskId: (pageTaskId: string) => void;
  setContentScriptReady: (ready: boolean) => void;
  setDownloadData: (data: DownloadItem | null) => void;
  setError: (error: string) => void;
  startDownloadWithData: (data: DownloadItem) => Promise<void>;
}

/**
 * 下载器初始化Hook
 * 负责页面初始化、消息监听和数据加载
 */
export function useDownloaderInit({
  state,
  setPageTaskId,
  setContentScriptReady,
  setDownloadData,
  setError,
  startDownloadWithData
}: UseDownloaderInitProps) {
  const cleanupFunctions = useRef<Array<() => void>>([]);
  const startDownloadWithDataRef = useRef(startDownloadWithData);

  // 更新ref以获取最新的函数
  useEffect(() => {
    startDownloadWithDataRef.current = startDownloadWithData;
  }, [startDownloadWithData]);

  // 初始化页面任务ID
  useEffect(() => {
    const pageTaskId = generatePageTaskId();
    setPageTaskId(pageTaskId);
  }, [setPageTaskId]);

  // 设置消息监听器
  useEffect(() => {
    const cleanup1 = createMessageListener(async (event) => {
      const { type, data } = event.data;

      if (type === 'CONTENT_SCRIPT_READY') {
        console.log('内容脚本已准备就绪');
        setContentScriptReady(true);
      } else if (type === 'HEADERS_SET_COMPLETED') {
        // 检查消息是否属于当前页面
        if (data && data.pageTaskId && data.pageTaskId === state.pageTaskId) {
          console.log('✅ 收到当前页面的请求头设置完成通知:', data);
          if (data.success) {
            console.log('✅ 请求头设置成功，可以开始下载');
          } else {
            console.error('❌ 请求头设置失败:', data.error);
          }
        } else {
          console.log('🚫 这是其他页面的请求头设置通知，当前页面忽略');
        }
      } else if (type === 'DOWNLOAD_DATA_RESPONSE' || type === 'SET_REQUEST_HEADERS_RESPONSE' ||
        type === 'DOWNLOAD_FILE_RESPONSE' || type === 'CLEANUP_REQUEST_HEADERS_RESPONSE') {
        // 这些是通过新的Chrome标准消息传递方式发送的响应
        // 检查消息是否属于当前页面
        if (data && data.pageTaskId && data.pageTaskId === state.pageTaskId) {
          console.log(`✅ 收到当前页面的${type}响应:`, data);
          // 触发相应的处理逻辑
          window.dispatchEvent(new CustomEvent(type, { detail: data }));
        } else {
          console.log(`🚫 忽略其他页面的${type}响应`);
        }
      }
    });

    cleanupFunctions.current.push(cleanup1);

    return () => {
      cleanupFunctions.current.forEach(cleanup => cleanup());
      cleanupFunctions.current = [];
    };
  }, [state.pageTaskId, setContentScriptReady]);

  // 从URL加载下载数据
  const loadDownloadDataFromUrl = useCallback(() => {
    try {
      const requestId = getRequestIdFromUrl();

      if (requestId && state.pageTaskId) {
        console.log('从URL获取请求ID:', requestId, '页面任务ID:', state.pageTaskId);

        const cleanup = requestDownloadData(
          requestId,
          state.pageTaskId,
          (downloadData: DownloadItem) => {
            console.log('收到下载数据，准备开始下载:', downloadData);
            setDownloadData(downloadData);

            // 自动开始下载
            setTimeout(async () => {
              console.log('自动开始下载...');
              await startDownloadWithDataRef.current(downloadData);
            }, 100); // 稍微延迟确保状态更新完成
          },
          (error: string) => {
            setError(error);
          }
        );

        cleanupFunctions.current.push(cleanup);
      } else {
        console.warn('URL中没有找到请求ID');
        setError('没有找到请求ID');
      }
    } catch (error) {
      console.error('解析URL参数失败:', error);
      setError('解析URL参数失败: ' + (error as Error).message);
    }
  }, [state.pageTaskId, setDownloadData, setError]);

  // 当内容脚本和页面都准备就绪时，开始加载下载数据
  useEffect(() => {
    if (state.contentScriptReady && state.pageReady && state.pageTaskId) {
      console.log('内容脚本和页面都已准备就绪，开始加载下载数据');
      loadDownloadDataFromUrl();
    }
  }, [state.contentScriptReady, state.pageReady, state.pageTaskId, loadDownloadDataFromUrl]);

  return {
    cleanupFunctions
  };
}
